/**
 * CJ Affiliate API Service
 * 
 * This service integrates with Commission Junction's API to fetch prices from
 * affiliate partners like Fanatical, Humble Bundle, GamesPlanet, and others.
 * 
 * Note: CJ API endpoints are currently under investigation. This implementation
 * provides a framework that can be easily updated once correct endpoints are confirmed.
 */

export interface CJGamePrice {
  store_name: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  store_url: string;
  affiliate_url: string;
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  region: string;
  advertiser_name: string;
  product_name: string;
}

interface CJSearchResult {
  products: Array<{
    'advertiser-name': string;
    'buy-url': string;
    'catalog-id': string;
    'currency': string;
    'description': string;
    'image-url': string;
    'in-stock': 'true' | 'false';
    'name': string;
    'price': string;
    'retail-price': string;
    'sale-price': string;
    'sku': string;
    'upc': string;
  }>;
}

export class CJApiService {
  private developerKey: string;
  private websiteId: string;
  private companyId: string;
  private baseUrl: string;
  private requestTimeout: number;
  private debugMode: boolean;

  constructor() {
    this.developerKey = process.env.CJ_DEVELOPER_KEY || '';
    this.websiteId = process.env.CJ_WEBSITE_ID || '';
    this.companyId = process.env.CJ_COMPANY_ID || '';
    this.baseUrl = process.env.CJ_API_BASE_URL || 'https://api.cj.com';
    this.requestTimeout = parseInt(process.env.CJ_REQUEST_TIMEOUT || '30000');
    
    // Force real data if user has approved affiliate programs
    const forceRealData = process.env.CJ_FORCE_REAL_DATA === 'true';
    this.debugMode = forceRealData ? false : (process.env.NODE_ENV === 'development');
    
    if (forceRealData) {
      console.log('🔥 CJ API: FORCED REAL DATA MODE - Mock data disabled, using live API calls');
    }
  }

  /**
   * Force real data mode (bypass mock data even in development)
   */
  setForceRealData(force: boolean = true): void {
    if (force) {
      console.log('🔥 FORCED REAL DATA MODE: Mock data disabled, will attempt real API calls');
    }
    this.debugMode = !force;
  }

  /**
   * Check if we're in mock mode
   */
  isMockMode(): boolean {
    return this.debugMode;
  }

  /**
   * Check if CJ API is properly configured
   */
  isConfigured(): boolean {
    const isValid = !!(this.developerKey && this.websiteId && this.companyId);
    
    if (!isValid && this.debugMode) {
      console.log('❌ CJ API não configurado:', {
        hasDeveloperKey: !!this.developerKey,
        hasWebsiteId: !!this.websiteId,
        hasCompanyId: !!this.companyId
      });
    }
    
    return isValid;
  }

  /**
   * Get region-specific currency mapping
   */
  private getRegionCurrency(regionCode: string): string {
    const regionToCurrency: Record<string, string> = {
      'us': 'USD',
      'gb': 'GBP',
      'de': 'EUR',
      'fr': 'EUR',
      'it': 'EUR',
      'es': 'EUR',
      'br': 'BRL',
      'ca': 'CAD',
      'au': 'AUD',
      'jp': 'JPY',
      'kr': 'KRW',
      'global': 'USD'
    };
    return regionToCurrency[regionCode] || 'USD';
  }

  /**
   * Generate a consistent hash from game name for realistic mock pricing
   */
  private generateGameHash(gameName: string): number {
    let hash = 0;
    for (let i = 0; i < gameName.length; i++) {
      const char = gameName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Generate realistic pricing based on currency and game hash
   */
  private generateRealisticPrice(gameHash: number, currency: string): number {
    const basePrices: Record<string, number[]> = {
      'USD': [9.99, 19.99, 29.99, 39.99, 49.99, 59.99],
      'EUR': [9.99, 19.99, 29.99, 39.99, 49.99, 59.99],
      'GBP': [7.99, 15.99, 24.99, 32.99, 39.99, 49.99],
      'BRL': [29.99, 49.99, 79.99, 99.99, 149.99, 199.99],
      'CAD': [12.99, 24.99, 39.99, 52.99, 64.99, 79.99],
      'AUD': [14.99, 29.99, 44.99, 59.99, 74.99, 89.99],
      'JPY': [1200, 2400, 3600, 4800, 6000, 7200],
      'KRW': [12000, 24000, 36000, 48000, 60000, 72000]
    };

    const prices = basePrices[currency] || basePrices['USD'];
    return prices[gameHash % prices.length];
  }

  /**
   * Generate affiliate tracking URL for CJ
   * In development mode, returns direct store URL for easier testing
   */
  private generateAffiliateUrl(buyUrl: string, advertiserName: string): string {
    try {
      // In development mode, use direct store URLs for easier testing
      if (this.debugMode) {
        console.log(`🔗 DEV MODE: Using direct store URL instead of affiliate link for ${advertiserName}`);
        return buyUrl;
      }

      // Production: CJ tracking URL format - multiple patterns to try
      const trackingPatterns = [
        `https://www.jdoqocy.com/click-${this.websiteId}-${Date.now()}?url=${encodeURIComponent(buyUrl)}`,
        `https://www.anrdoezrs.net/click-${this.websiteId}-${Date.now()}?url=${encodeURIComponent(buyUrl)}`,
        `https://www.dpbolvw.net/click-${this.websiteId}-${Date.now()}?url=${encodeURIComponent(buyUrl)}`
      ];

      // Use first pattern as default
      return trackingPatterns[0];
    } catch (error) {
      console.error('❌ Error generating affiliate URL:', error);
      return buyUrl; // Fallback to original URL
    }
  }

  /**
   * Mock implementation for testing - simulates Fanatical/CJ responses
   * This allows us to test the integration while API endpoints are being investigated
   */
  private getMockFanaticalData(gameName: string, regionCode: string): CJGamePrice[] {
    if (!this.debugMode) {
      return []; // Only return mock data in development
    }

    const currency = this.getRegionCurrency(regionCode);

    // Generate more realistic pricing based on game name hash for consistency
    const gameHash = this.generateGameHash(gameName);
    const basePrice = this.generateRealisticPrice(gameHash, currency);
    const discountPrice = basePrice * (0.6 + (gameHash % 30) / 100); // 60-90% of original price
    const discountPercentage = Math.round(((basePrice - discountPrice) / basePrice) * 100);

    const storeUrl = `https://www.fanatical.com/en/search?search=${encodeURIComponent(gameName)}`;
    const affiliateUrl = this.generateAffiliateUrl(storeUrl, 'Fanatical');

    console.log(`🧪 MOCK FANATICAL: Generated price for "${gameName}": ${currency} ${discountPrice.toFixed(2)} (${discountPercentage}% off ${basePrice.toFixed(2)})`);
    console.log(`🔗 MOCK FANATICAL: Store URL: ${storeUrl}`);
    console.log(`🎯 MOCK FANATICAL: ${this.debugMode ? 'Direct' : 'Affiliate'} URL: ${affiliateUrl}`);

    return [
      {
        store_name: 'Fanatical',
        price: Math.round(discountPrice * 100) / 100, // Round to 2 decimals
        original_price: Math.round(basePrice * 100) / 100,
        discount_percentage: discountPercentage,
        store_url: storeUrl,
        affiliate_url: affiliateUrl,
        currency: currency,
        availability: 'available',
        region: regionCode,
        advertiser_name: 'Fanatical.com',
        product_name: `${gameName} (Mock)`
      }
    ];
  }

  /**
   * Mock implementation for testing - simulates Gamivo/CJ responses
   * This allows us to test the integration while API endpoints are being investigated
   */
  private getMockGamivoData(gameName: string, regionCode: string): CJGamePrice[] {
    if (!this.debugMode) {
      return []; // Only return mock data in development
    }

    const currency = this.getRegionCurrency(regionCode);

    // Generate more realistic pricing based on game name hash for consistency
    const gameHash = this.generateGameHash(gameName);
    const basePrice = this.generateRealisticPrice(gameHash + 1, currency); // Offset for different pricing
    const discountPrice = basePrice * (0.55 + (gameHash % 25) / 100); // 55-80% of original price (Gamivo often has aggressive pricing)
    const discountPercentage = Math.round(((basePrice - discountPrice) / basePrice) * 100);

    // Improved Gamivo URL with better search parameters
    const cleanGameName = gameName.replace(/[^\w\s]/g, '').trim();
    const storeUrl = `https://www.gamivo.com/product/${encodeURIComponent(cleanGameName.replace(/\s+/g, '-').toLowerCase())}`;
    const searchUrl = `https://www.gamivo.com/search?query=${encodeURIComponent(gameName)}`;
    
    // Use search URL for mock but try product URL first
    const finalStoreUrl = storeUrl;
    const affiliateUrl = this.generateAffiliateUrl(finalStoreUrl, 'Gamivo');

    console.log(`🧪 MOCK GAMIVO: Generated price for "${gameName}": ${currency} ${discountPrice.toFixed(2)} (${discountPercentage}% off ${basePrice.toFixed(2)})`);
    console.log(`🔗 MOCK GAMIVO: Store URL: ${storeUrl}`);
    console.log(`🎯 MOCK GAMIVO: ${this.debugMode ? 'Direct' : 'Affiliate'} URL: ${affiliateUrl}`);

    return [
      {
        store_name: 'Gamivo',
        price: Math.round(discountPrice * 100) / 100, // Round to 2 decimals
        original_price: Math.round(basePrice * 100) / 100,
        discount_percentage: discountPercentage,
        store_url: storeUrl,
        affiliate_url: affiliateUrl,
        currency: currency,
        availability: 'available',
        region: regionCode,
        advertiser_name: 'Gamivo.com',
        product_name: `${gameName} (Mock)`
      }
    ];
  }

  /**
   * Test CJ API connection with multiple endpoint attempts
   */
  async testConnection(): Promise<{ success: boolean; endpoint?: string; message: string }> {
    if (!this.isConfigured()) {
      return { success: false, message: 'CJ API não configurado - verifique as credenciais' };
    }

    // Endpoints to test based on CJ API documentation for product search
    const testEndpoints = [
      `${this.baseUrl}/v3/product-search`,
      `${this.baseUrl}/v2/product-search`,
      `${this.baseUrl}/v3/product-catalog`,
      `${this.baseUrl}/v2/product-catalog`,
      'https://product-search.api.cj.com/v2/product-search',
      'https://product-catalog.api.cj.com/v2/product-catalog'
    ];

    for (const endpoint of testEndpoints) {
      try {
        console.log(`🔍 Testing CJ endpoint: ${endpoint}`);
        
        const response = await fetch(`${endpoint}?keywords=${encodeURIComponent('game')}&records-per-page=1&advertiser-ids=joined`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.developerKey}`,
            'Accept': 'application/xml',
            'User-Agent': 'CriticalPixel/1.0',
            'Content-Type': 'application/xml'
          },
          signal: AbortSignal.timeout(this.requestTimeout)
        });

        console.log(`📊 Response: ${response.status} ${response.statusText}`);

        if (response.status === 200) {
          const text = await response.text();
          if (text.includes('<?xml') || text.includes('{')) {
            return { 
              success: true, 
              endpoint, 
              message: `Conectado com sucesso ao endpoint: ${endpoint}` 
            };
          }
        } else if (response.status === 400) {
          // Bad request might mean the endpoint exists but parameters are wrong
          const errorText = await response.text();
          if (errorText.includes('Invalid Key') || errorText.includes('keywords')) {
            return { 
              success: true, 
              endpoint, 
              message: `Endpoint funcional encontrado (erro de parâmetros): ${endpoint}` 
            };
          }
        }
      } catch (error) {
        console.log(`❌ Endpoint ${endpoint} failed:`, error instanceof Error ? error.message : String(error));
      }
    }

    return { 
      success: false, 
      message: 'Nenhum endpoint CJ funcional encontrado. Usando dados mock para desenvolvimento.' 
    };
  }

  /**
   * Search for games in CJ affiliate network
   * Currently returns mock data while endpoints are being investigated
   */
  async searchGames(gameName: string, regionCode: string = 'global'): Promise<CJSearchResult | null> {
    if (!this.isConfigured()) {
      console.error('❌ CJ API não está configurado');
      return null;
    }

    try {
      // First test if we have a working endpoint
      const connectionTest = await this.testConnection();
      
      if (connectionTest.success && connectionTest.endpoint && !this.debugMode) {
        // Try to make real API call when force real data is enabled
        console.log(`🔥 REAL DATA MODE: Attempting real CJ API call for "${gameName}"`);
        
        try {
          const response = await fetch(`${connectionTest.endpoint}?keywords=${encodeURIComponent(gameName)}&records-per-page=50&advertiser-ids=joined`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${this.developerKey}`,
              'Accept': 'application/xml',
              'User-Agent': 'CriticalPixel/1.0',
              'Content-Type': 'application/xml'
            },
            signal: AbortSignal.timeout(this.requestTimeout)
          });

          if (response.ok) {
            const xmlData = await response.text();
            console.log('📥 CJ API Response received, parsing...');
            
            // Parse XML response and convert to our format
            const parsedData = this.parseXMLResponse(xmlData);
            if (parsedData && parsedData.products && parsedData.products.length > 0) {
              console.log(`✅ CJ API: Parsed ${parsedData.products.length} products from XML`);
              return parsedData;
            } else {
              console.log('⚠️ CJ API: No products found in XML response');
            }
          }
        } catch (apiError) {
          console.error('❌ Real CJ API call failed:', apiError);
        }
      }

      // Return mock data for development/testing
      if (this.debugMode) {
        console.log(`🧪 Retornando dados mock para "${gameName}" em região ${regionCode}`);
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        return {
          products: [
            {
              'advertiser-name': 'Fanatical.com',
              'buy-url': `https://www.fanatical.com/en/search?search=${encodeURIComponent(gameName)}`,
              'catalog-id': 'mock-catalog-id',
              'currency': this.getRegionCurrency(regionCode),
              'description': `${gameName} - Digital PC Game Key`,
              'image-url': 'https://via.placeholder.com/150x200?text=Game',
              'in-stock': 'true',
              'name': `${gameName} (Mock)`,
              'price': '29.99',
              'retail-price': '39.99',
              'sale-price': '19.99',
              'sku': 'mock-sku-123',
              'upc': 'mock-upc-456'
            },
            {
              'advertiser-name': 'Gamivo.com',
              'buy-url': `https://www.gamivo.com/search?query=${encodeURIComponent(gameName)}`,
              'catalog-id': 'mock-gamivo-catalog-id',
              'currency': this.getRegionCurrency(regionCode),
              'description': `${gameName} - Digital Game Key`,
              'image-url': 'https://via.placeholder.com/150x200?text=Game',
              'in-stock': 'true',
              'name': `${gameName} (Mock Gamivo)`,
              'price': '25.99',
              'retail-price': '45.99',
              'sale-price': '16.99',
              'sku': 'mock-gamivo-sku-456',
              'upc': 'mock-gamivo-upc-789'
            }
          ]
        };
      }

      console.log(`⚠️ CJ API em modo produção - dados reais não disponíveis ainda`);
      return null;

    } catch (error) {
      console.error('❌ CJ API Search Error:', error);
      return null;
    }
  }

  /**
   * Get game price specifically from Fanatical via CJ
   */
  async getFanaticalPrice(gameName: string, regionCode: string = 'global'): Promise<CJGamePrice | null> {
    try {
      // In development, return mock data
      if (this.debugMode) {
        const mockData = this.getMockFanaticalData(gameName, regionCode);
        if (mockData.length > 0) {
          console.log(`🧪 Mock Fanatical price: ${mockData[0].currency} ${mockData[0].price} para "${gameName}"`);
          return mockData[0];
        }
      }

      const searchResult = await this.searchGames(gameName, regionCode);
      
      if (!searchResult || !searchResult.products) {
        return null;
      }

      // Filter for Fanatical products
      const fanaticalProducts = searchResult.products.filter(product => 
        product['advertiser-name'].toLowerCase().includes('fanatical')
      );

      if (fanaticalProducts.length === 0) {
        console.log(`⚠️ CJ API: Nenhum produto Fanatical encontrado para "${gameName}"`);
        return null;
      }

      // Get the best match (first result)
      const product = fanaticalProducts[0];
      
      // Parse pricing
      const salePrice = parseFloat(product['sale-price'] || product['price'] || '0');
      const retailPrice = parseFloat(product['retail-price'] || '0');
      
      if (salePrice === 0) {
        console.log(`⚠️ CJ API: Preço inválido para produto Fanatical "${product.name}"`);
        return null;
      }

      // Calculate discount
      let discountPercentage = 0;
      if (retailPrice > salePrice && retailPrice > 0) {
        discountPercentage = Math.round(((retailPrice - salePrice) / retailPrice) * 100);
      }

      // Generate affiliate URL
      const affiliateUrl = this.generateAffiliateUrl(product['buy-url'], product['advertiser-name']);

      const gamePrice: CJGamePrice = {
        store_name: 'Fanatical',
        price: salePrice,
        original_price: retailPrice > salePrice ? retailPrice : undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: product['buy-url'],
        affiliate_url: affiliateUrl,
        currency: product.currency || this.getRegionCurrency(regionCode),
        availability: product['in-stock'] === 'true' ? 'available' : 'out_of_stock',
        region: regionCode,
        advertiser_name: product['advertiser-name'],
        product_name: product.name
      };

      console.log(`💰 CJ/Fanatical: Preço encontrado ${gamePrice.currency} ${gamePrice.price} para "${gameName}"`);
      
      return gamePrice;

    } catch (error) {
      console.error('❌ CJ Fanatical Price Error:', error);
      return null;
    }
  }

  /**
   * Get game price specifically from Gamivo via CJ
   */
  async getGamivoPrice(gameName: string, regionCode: string = 'global'): Promise<CJGamePrice | null> {
    try {
      // In development, return mock data
      if (this.debugMode) {
        const mockData = this.getMockGamivoData(gameName, regionCode);
        if (mockData.length > 0) {
          console.log(`🧪 Mock Gamivo price: ${mockData[0].currency} ${mockData[0].price} para "${gameName}"`);
          return mockData[0];
        }
      }

      const searchResult = await this.searchGames(gameName, regionCode);
      
      if (!searchResult || !searchResult.products) {
        return null;
      }

      // Filter for Gamivo products
      const gamivoProducts = searchResult.products.filter(product => 
        product['advertiser-name'].toLowerCase().includes('gamivo')
      );

      if (gamivoProducts.length === 0) {
        console.log(`⚠️ CJ API: Nenhum produto Gamivo encontrado para "${gameName}"`);
        return null;
      }

      // Get the best match (first result)
      const product = gamivoProducts[0];
      
      // Parse pricing
      const salePrice = parseFloat(product['sale-price'] || product['price'] || '0');
      const retailPrice = parseFloat(product['retail-price'] || '0');
      
      if (salePrice === 0) {
        console.log(`⚠️ CJ API: Preço inválido para produto Gamivo "${product.name}"`);
        return null;
      }

      // Calculate discount
      let discountPercentage = 0;
      if (retailPrice > salePrice && retailPrice > 0) {
        discountPercentage = Math.round(((retailPrice - salePrice) / retailPrice) * 100);
      }

      // Generate affiliate URL
      const affiliateUrl = this.generateAffiliateUrl(product['buy-url'], product['advertiser-name']);

      const gamePrice: CJGamePrice = {
        store_name: 'Gamivo',
        price: salePrice,
        original_price: retailPrice > salePrice ? retailPrice : undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: product['buy-url'],
        affiliate_url: affiliateUrl,
        currency: product.currency || this.getRegionCurrency(regionCode),
        availability: product['in-stock'] === 'true' ? 'available' : 'out_of_stock',
        region: regionCode,
        advertiser_name: product['advertiser-name'],
        product_name: product.name
      };

      console.log(`💰 CJ/Gamivo: Preço encontrado ${gamePrice.currency} ${gamePrice.price} para "${gameName}"`);
      
      return gamePrice;

    } catch (error) {
      console.error('❌ CJ Gamivo Price Error:', error);
      return null;
    }
  }

  /**
   * Get game prices from all CJ gaming affiliates
   */
  async getAllGamingPrices(gameName: string, regionCode: string = 'global'): Promise<CJGamePrice[]> {
    try {
      // In development, return mock data for multiple stores
      if (this.debugMode) {
        const currency = this.getRegionCurrency(regionCode);
        const gameHash = this.generateGameHash(gameName);

        const mockStores = [
          {
            name: 'Fanatical',
            baseUrl: 'https://www.fanatical.com/en/search?search=',
            priceMultiplier: 0.75 // Usually cheapest
          },
          {
            name: 'Humble Bundle',
            baseUrl: 'https://www.humblebundle.com/store/search?search=',
            priceMultiplier: 0.85 // Mid-range pricing
          },
          {
            name: 'GamesPlanet',
            baseUrl: 'https://www.gamesplanet.com/search?query=',
            priceMultiplier: 0.80 // Good deals
          },
          {
            name: 'Gamivo',
            baseUrl: 'https://www.gamivo.com/search?query=',
            priceMultiplier: 0.70 // Often very competitive pricing
          }
        ];

        console.log(`🌍 MOCK CJ STORES: Generating prices for "${gameName}" in ${currency} (${regionCode})`);

        return mockStores.map((store, index) => {
          const basePrice = this.generateRealisticPrice(gameHash + index, currency);
          const discountPrice = Math.round(basePrice * store.priceMultiplier * 100) / 100;
          const discountPercentage = Math.round(((basePrice - discountPrice) / basePrice) * 100);

          const storeUrl = `${store.baseUrl}${encodeURIComponent(gameName)}`;
          const affiliateUrl = this.generateAffiliateUrl(storeUrl, store.name);

          console.log(`💰 MOCK ${store.name}: ${currency} ${discountPrice} (${discountPercentage}% off ${basePrice})`);
          console.log(`🔗 MOCK ${store.name}: ${this.debugMode ? 'Direct' : 'Affiliate'} URL: ${affiliateUrl}`);

          return {
            store_name: store.name,
            price: discountPrice,
            original_price: basePrice,
            discount_percentage: discountPercentage,
            store_url: storeUrl,
            affiliate_url: affiliateUrl,
            currency: currency,
            availability: 'available' as const,
            region: regionCode,
            advertiser_name: `${store.name}.com`,
            product_name: `${gameName} (Mock)`
          };
        });
      }

      const searchResult = await this.searchGames(gameName, regionCode);
      
      if (!searchResult || !searchResult.products) {
        return [];
      }

      const gamingAdvertisers = [
        'fanatical',
        'humble',
        'gamesplanet', 
        'greenmangaming',
        'cdkeys',
        'voidu',
        'eneba',
        'gamivo'
      ];

      const gamingProducts = searchResult.products.filter(product => 
        gamingAdvertisers.some(advertiser => 
          product['advertiser-name'].toLowerCase().includes(advertiser)
        )
      );

      const products: CJGamePrice[] = [];
      console.log(`🔍 CJ: Found ${gamingProducts.length} gaming products`);

      for (let i = 0; i < gamingProducts.length; i++) {
        const rawProduct = gamingProducts[i];
        
        const salePrice = parseFloat(rawProduct['sale-price'] || rawProduct['price'] || '0');
        const retailPrice = parseFloat(rawProduct['retail-price'] || '0');
        
        if (salePrice === 0) continue;

        // Calculate discount
        let discountPercentage = 0;
        if (retailPrice > salePrice && retailPrice > 0) {
          discountPercentage = Math.round(((retailPrice - salePrice) / retailPrice) * 100);
        }

        // Generate affiliate URL
        const affiliateUrl = this.generateAffiliateUrl(rawProduct['buy-url'], rawProduct['advertiser-name']);

        // Clean store name
        const storeName = this.cleanStoreName(rawProduct['advertiser-name']);

        const gamePrice: CJGamePrice = {
          store_name: storeName,
          price: salePrice,
          original_price: retailPrice > salePrice ? retailPrice : undefined,
          discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
          store_url: rawProduct['buy-url'],
          affiliate_url: affiliateUrl,
          currency: rawProduct['currency'] || this.getRegionCurrency(regionCode),
          availability: rawProduct['in-stock'] === 'true' ? 'available' : 'out_of_stock',
          region: regionCode,
          advertiser_name: rawProduct['advertiser-name'],
          product_name: rawProduct['name']
        };

        products.push(gamePrice);
        console.log(`💰 CJ/${storeName}: Preço encontrado ${gamePrice.currency} ${gamePrice.price}`);
      }

      console.log(`✅ CJ API: ${products.length} preços de gaming encontrados para "${gameName}"`);
      return products;

    } catch (error) {
      console.error('❌ CJ All Gaming Prices Error:', error);
      return [];
    }
  }

  /**
   * Clean and normalize store names
   */
  private cleanStoreName(advertiserName: string): string {
    const nameMapping: Record<string, string> = {
      'Fanatical.com': 'Fanatical',
      'Humble Bundle': 'Humble Bundle',
      'GamesPlanet': 'GamesPlanet',
      'GreenManGaming': 'Green Man Gaming',
      'CDKeys': 'CDKeys',
      'Voidu': 'Voidu',
      'Eneba': 'Eneba',
      'Gamivo.com': 'Gamivo'
    };

    // Check exact matches first
    if (nameMapping[advertiserName]) {
      return nameMapping[advertiserName];
    }

    // Check partial matches
    for (const [key, value] of Object.entries(nameMapping)) {
      if (advertiserName.toLowerCase().includes(key.toLowerCase())) {
        return value;
      }
    }

    // Fallback: clean the original name
    return advertiserName
      .replace(/\.com$/i, '')
      .replace(/[^\w\s]/g, '')
      .trim();
  }

  /**
   * Format price with currency symbol
   */
  formatPrice(price: number, currency: string): string {
    try {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(price);
    } catch (error) {
      return `${currency} ${price.toFixed(2)}`;
    }
  }

  /**
   * Parse XML response and convert to our format
   */
  private parseXMLResponse(xmlData: string): CJSearchResult | null {
    try {
      console.log(`🔍 CJ XML Parser: Processing ${xmlData.length} characters of XML data`);
      
      // Log first 500 chars for debugging
      console.log(`📄 XML Preview: ${xmlData.substring(0, 500)}...`);
      
      // Try to parse XML using DOMParser (works in browser and Node.js with appropriate polyfill)
      let parser: DOMParser;
      if (typeof window !== 'undefined') {
        // Browser environment
        parser = new DOMParser();
      } else {
        // Node.js environment - use xmldom package if available, otherwise simple string parsing
        try {
          const { DOMParser: NodeDOMParser } = require('xmldom');
          parser = new NodeDOMParser();
        } catch (e) {
          console.log('📋 CJ XML Parser: Using fallback string parsing (xmldom not available)');
          return this.parseXMLWithStringMethods(xmlData);
        }
      }

      const doc = parser.parseFromString(xmlData, 'text/xml');
      
      // Check for parsing errors
      const parserError = doc.getElementsByTagName('parsererror')[0];
      if (parserError) {
        console.error('❌ CJ XML Parser: XML parsing error:', parserError.textContent);
        return this.parseXMLWithStringMethods(xmlData);
      }

      // Look for product elements - CJ API may use different XML structures
      const productElements = doc.getElementsByTagName('product') || 
                            doc.getElementsByTagName('item') ||
                            doc.getElementsByTagName('advertiser');

      if (!productElements || productElements.length === 0) {
        console.log('⚠️ CJ XML Parser: No product elements found, trying alternative parsing');
        return this.parseXMLWithStringMethods(xmlData);
      }

      const products: CJSearchResult['products'] = [];
      console.log(`🔍 CJ XML Parser: Found ${productElements.length} product elements`);

      for (let i = 0; i < productElements.length; i++) {
        const productElement = productElements[i];
        
        const inStockValue = this.getElementText(productElement, 'in-stock') || 
                           this.getElementText(productElement, 'inStock') || 'true';
        
        const product: CJSearchResult['products'][0] = {
          'advertiser-name': this.getElementText(productElement, 'advertiser-name') || 
                           this.getElementText(productElement, 'advertiserName') || 
                           'Unknown',
          'buy-url': this.getElementText(productElement, 'buy-url') || 
                    this.getElementText(productElement, 'buyUrl') || 
                    this.getElementText(productElement, 'link') || '',
          'catalog-id': this.getElementText(productElement, 'catalog-id') || 
                       this.getElementText(productElement, 'catalogId') || 
                       `mock-${i}`,
          'currency': this.getElementText(productElement, 'currency') || 'USD',
          'description': this.getElementText(productElement, 'description') || 
                        this.getElementText(productElement, 'name') || '',
          'image-url': this.getElementText(productElement, 'image-url') || 
                      this.getElementText(productElement, 'imageUrl') || '',
          'in-stock': (inStockValue.toLowerCase() === 'false') ? 'false' : 'true',
          'name': this.getElementText(productElement, 'name') || 
                 this.getElementText(productElement, 'title') || '',
          'price': this.getElementText(productElement, 'price') || '0',
          'retail-price': this.getElementText(productElement, 'retail-price') || 
                         this.getElementText(productElement, 'retailPrice') || '0',
          'sale-price': this.getElementText(productElement, 'sale-price') || 
                       this.getElementText(productElement, 'salePrice') || '0',
          'sku': this.getElementText(productElement, 'sku') || '',
          'upc': this.getElementText(productElement, 'upc') || ''
        };

        // Only add products that have essential data
        if (product['name'] && product['price'] !== '0') {
          products.push(product);
          console.log(`✅ CJ XML Parser: Product ${i + 1}: ${product['name']} - ${product['currency']} ${product['price']}`);
        }
      }

      if (products.length > 0) {
        console.log(`🎯 CJ XML Parser: Successfully parsed ${products.length} valid products`);
        return { products };
      } else {
        console.log('⚠️ CJ XML Parser: No valid products found in XML');
        return null;
      }

    } catch (error) {
      console.error('❌ CJ XML Parser Error:', error);
      console.log('🔄 CJ XML Parser: Falling back to string parsing method');
      return this.parseXMLWithStringMethods(xmlData);
    }
  }

  /**
   * Fallback XML parsing using string methods
   */
  private parseXMLWithStringMethods(xmlData: string): CJSearchResult | null {
    try {
      console.log('🔧 CJ String Parser: Using regex-based XML parsing');
      
      // Simple regex-based parsing for basic XML structures
      const productMatches = xmlData.match(/<product[^>]*>[\s\S]*?<\/product>/gi) ||
                            xmlData.match(/<item[^>]*>[\s\S]*?<\/item>/gi) ||
                            xmlData.match(/<advertiser[^>]*>[\s\S]*?<\/advertiser>/gi);

      if (!productMatches) {
        console.log('⚠️ CJ String Parser: No product blocks found in XML');
        return null;
      }

      const products: CJSearchResult['products'] = [];
      console.log(`🔍 CJ String Parser: Found ${productMatches.length} product blocks`);

      productMatches.forEach((productXml, index) => {
        const inStockValue = this.extractXMLValue(productXml, 'in-stock') || 
                           this.extractXMLValue(productXml, 'inStock') || 'true';

        const product: CJSearchResult['products'][0] = {
          'advertiser-name': this.extractXMLValue(productXml, 'advertiser-name') || 
                           this.extractXMLValue(productXml, 'advertiserName') || 
                           'Unknown Store',
          'buy-url': this.extractXMLValue(productXml, 'buy-url') || 
                    this.extractXMLValue(productXml, 'buyUrl') || 
                    this.extractXMLValue(productXml, 'link') || '',
          'catalog-id': this.extractXMLValue(productXml, 'catalog-id') || 
                       this.extractXMLValue(productXml, 'catalogId') || 
                       `fallback-${index}`,
          'currency': this.extractXMLValue(productXml, 'currency') || 'USD',
          'description': this.extractXMLValue(productXml, 'description') || '',
          'image-url': this.extractXMLValue(productXml, 'image-url') || 
                      this.extractXMLValue(productXml, 'imageUrl') || '',
          'in-stock': (inStockValue.toLowerCase() === 'false') ? 'false' : 'true',
          'name': this.extractXMLValue(productXml, 'name') || 
                 this.extractXMLValue(productXml, 'title') || '',
          'price': this.extractXMLValue(productXml, 'price') || '0',
          'retail-price': this.extractXMLValue(productXml, 'retail-price') || 
                         this.extractXMLValue(productXml, 'retailPrice') || '0',
          'sale-price': this.extractXMLValue(productXml, 'sale-price') || 
                       this.extractXMLValue(productXml, 'salePrice') || '0',
          'sku': this.extractXMLValue(productXml, 'sku') || '',
          'upc': this.extractXMLValue(productXml, 'upc') || ''
        };

        if (product['name'] && product['price'] !== '0') {
          products.push(product);
          console.log(`✅ CJ String Parser: Product ${index + 1}: ${product['name']} - ${product['currency']} ${product['price']}`);
        }
      });

      return products.length > 0 ? { products } : null;

    } catch (error) {
      console.error('❌ CJ String Parser Error:', error);
      return null;
    }
  }

  /**
   * Helper to get text content from XML element
   */
  private getElementText(parent: Element, tagName: string): string {
    const element = parent.getElementsByTagName(tagName)[0];
    return element ? element.textContent || '' : '';
  }

  /**
   * Helper to extract value using regex from XML string
   */
  private extractXMLValue(xml: string, tagName: string): string {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = xml.match(regex);
    return match ? match[1].trim() : '';
  }
}

// Export singleton instance
export const cjApiService = new CJApiService();