'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Terminal, TrendingDown, TrendingUp, Minus, DollarSign } from 'lucide-react';
import { useCurrency } from '@/contexts/currency-context';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
// Remove direct service import - we'll use API calls instead

interface SteamTrackingData {
  gameId: string;
  regionCode: string;
  currency: string;
  hasData: boolean;
  prices: {
    original?: {
      cents: number;
      formatted: string;
      updated: string;
    };
    allTimeLow?: {
      cents: number;
      formatted: string;
      updated: string;
    };
    allTimeHigh?: {
      cents: number;
      formatted: string;
      updated: string;
    };
  };
  chartData: Array<{
    type: 'original' | 'low' | 'high';
    price_cents: number;
    price_formatted: string;
    recorded_at: string;
    color: string;
  }>;
}

interface SteamPriceHistoryChartProps {
  gameId: string;
  className?: string;
  daysBack?: number;
}

export default function SteamPriceHistoryChart({ 
  gameId, 
  className = '',
  daysBack = 90 
}: SteamPriceHistoryChartProps) {
  const { selectedCurrency } = useCurrency();
  const [data, setData] = useState<SteamTrackingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Map currency to region codes (same as GamePricesWidget)
  const getCurrencyRegion = (currencyCode: string): string => {
    const currencyToRegion: Record<string, string> = {
      'USD': 'us',
      'EUR': 'de',
      'GBP': 'gb',
      'JPY': 'jp',
      'KRW': 'kr',
      'CNY': 'cn',
      'RUB': 'ru',
      'BRL': 'br',
      'MXN': 'mx',
      'CAD': 'ca',
      'AUD': 'au',
      'INR': 'in',
    };
    return currencyToRegion[currencyCode] || 'us';
  };

  const regionCode = getCurrencyRegion(selectedCurrency || 'USD');

  const fetchPriceHistory = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(
        `/api/games/${gameId}/steam-tracking?region=${regionCode}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch Steam price tracking');
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error || 'Failed to load Steam price tracking');
      }
    } catch (err) {
      console.error('Error fetching Steam price tracking:', err);
      setError(err instanceof Error ? err.message : 'Failed to load Steam price tracking');
    } finally {
      setLoading(false);
    }
  }, [gameId, regionCode]);

  useEffect(() => {
    fetchPriceHistory();
  }, [fetchPriceHistory, selectedCurrency, regionCode]);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'yesterday';
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      }).toLowerCase();
    }
  };

  const formatPrice = (priceCents: number, currency: string): string => {
    const price = priceCents / 100;
    
    switch (currency) {
      case 'BRL':
        return `R$ ${price.toFixed(2).replace('.', ',')}`;
      case 'USD':
        return `$${price.toFixed(2)}`;
      case 'EUR':
        return `€${price.toFixed(2)}`;
      case 'GBP':
        return `£${price.toFixed(2)}`;
      case 'JPY':
        return `¥${Math.round(price)}`;
      default:
        return `${price.toFixed(2)} ${currency}`;
    }
  };

  const formatPriceShort = (priceCents: number, currency: string): string => {
    const price = priceCents / 100;
    
    if (price >= 1000) {
      return `${(price / 1000).toFixed(1)}K`;
    }
    
    switch (currency) {
      case 'BRL':
        return `R$${price.toFixed(0)}`;
      case 'USD':
        return `$${price.toFixed(0)}`;
      case 'EUR':
        return `€${price.toFixed(0)}`;
      case 'GBP':
        return `£${price.toFixed(0)}`;
      case 'JPY':
        return `¥${Math.round(price)}`;
      default:
        return `${price.toFixed(0)}`;
    }
  };

  if (loading && !data) {
    return (
      <motion.div 
        className={`bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.01 }}
      >
        {/* Terminal Header */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-slate-700/30">
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-violet-400" />
            <span className="text-xs text-slate-400">steam.priceHistory.loading()</span>
          </div>
          <div className="flex gap-1">
            <div className="w-2 h-2 rounded-full bg-red-500/60"></div>
            <div className="w-2 h-2 rounded-full bg-yellow-500/60"></div>
            <div className="w-2 h-2 rounded-full bg-green-500/60"></div>
          </div>
        </div>
        
        <div className="animate-pulse space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-violet-400 text-xs">$</span>
            <div className="h-3 bg-slate-700/50 rounded w-32"></div>
          </div>
          <div className="h-20 bg-slate-700/30 rounded border border-slate-700/50"></div>
          <div className="flex justify-between">
            <div className="h-2 bg-slate-700/50 rounded w-16"></div>
            <div className="h-2 bg-slate-700/50 rounded w-12"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  if (error || !data) {
    return (
      <motion.div 
        className={`bg-slate-800/40 border border-red-500/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.01 }}
      >
        {/* Terminal Header */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-red-500/20">
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-red-400" />
            <span className="text-xs text-slate-400">steam.priceHistory.error()</span>
          </div>
          <div className="flex gap-1">
            <div className="w-2 h-2 rounded-full bg-red-500"></div>
            <div className="w-2 h-2 rounded-full bg-slate-600"></div>
            <div className="w-2 h-2 rounded-full bg-slate-600"></div>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-red-400 text-xs">!</span>
            <span className="text-red-400 text-xs">Error:</span>
            <span className="text-slate-300 text-xs">priceHistory.fetch() failed</span>
          </div>
          <div className="text-xs text-slate-500">
            // No price history data available
          </div>
        </div>
      </motion.div>
    );
  }

  if (!data || !data.hasData || !data.chartData || data.chartData.length === 0) {
    return (
      <motion.div 
        className={`bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.01 }}
      >
        {/* Terminal Header */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-slate-700/30">
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-violet-400" />
            <span className="text-xs text-slate-400">steam.priceHistory.empty({selectedCurrency})</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-yellow-400 text-xs">⚠</span>
            <span className="text-slate-300 text-xs">No Steam price data for {selectedCurrency}</span>
          </div>
          <div className="text-xs text-slate-500">
            // Steam prices must be fetched first for {regionCode.toUpperCase()} region
          </div>
          <div className="text-xs text-slate-400 mt-2">
            Historical data will appear after Steam price updates
          </div>
        </div>
      </motion.div>
    );
  }

  // Calculate trend between high and low
  const latestPrice = data.prices?.allTimeHigh?.cents || 0;
  const earliestPrice = data.prices?.allTimeLow?.cents || 0;
  const trendPercentage = earliestPrice > 0 ? ((latestPrice - earliestPrice) / earliestPrice) * 100 : 0;
  
  const getTrendIcon = () => {
    if (Math.abs(trendPercentage) < 1) return Minus;
    return trendPercentage >= 0 ? TrendingUp : TrendingDown;
  };
  
  const getTrendColor = () => {
    if (Math.abs(trendPercentage) < 1) return 'text-slate-400';
    return trendPercentage >= 0 ? 'text-red-400' : 'text-green-400'; // Red for price increase, green for decrease
  };

  const TrendIcon = getTrendIcon();

  // Create ladder chart data - always start with original, then show changes
  const createLadderChartData = () => {
    const points = data.chartData || [];
    if (points.length === 0) return [];

    // Sort by date first
    const sortedPoints = points.sort((a, b) => 
      new Date(a.recorded_at).getTime() - new Date(b.recorded_at).getTime()
    );

    // Find original price to use as baseline
    const originalPoint = points.find(p => p.type === 'original');
    const lowPoint = points.find(p => p.type === 'low');
    const highPoint = points.find(p => p.type === 'high');

    if (!originalPoint) return [];

    const originalPrice = originalPoint.price_cents / 100;
    
    // Create a sequence showing price changes over time
    const ladderData = [];
    
    // Add starting point with original price
    ladderData.push({
      type: 'original',
      price: originalPrice,
      priceFormatted: originalPoint.price_formatted,
      formattedDate: formatDate(originalPoint.recorded_at),
      recorded_at: originalPoint.recorded_at,
      color: '#8b5cf6',
      dotSize: 4,
      step: 0
    });

    // Add low point if it exists and is different from original
    if (lowPoint && lowPoint.price_cents !== originalPoint.price_cents) {
      ladderData.push({
        type: 'low',
        price: lowPoint.price_cents / 100,
        priceFormatted: lowPoint.price_formatted,
        formattedDate: formatDate(lowPoint.recorded_at),
        recorded_at: lowPoint.recorded_at,
        color: '#10b981',
        dotSize: 6,
        step: 1
      });
    }

    // Add high point if it exists and is different from original
    if (highPoint && highPoint.price_cents !== originalPoint.price_cents) {
      ladderData.push({
        type: 'high',
        price: highPoint.price_cents / 100,
        priceFormatted: highPoint.price_formatted,
        formattedDate: formatDate(highPoint.recorded_at),
        recorded_at: highPoint.recorded_at,
        color: '#ef4444',
        dotSize: 6,
        step: 2
      });
    }

    return ladderData.sort((a, b) => a.step - b.step);
  };

  const chartData = createLadderChartData();

  const currency = data.currency || 'BRL';
  const lowestPrice = data.prices?.allTimeLow?.cents ? data.prices.allTimeLow.cents / 100 : undefined;

  return (
    <motion.div 
      className={`bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.01 }}
    >
      {/* Terminal Header */}
      <div className="flex items-center justify-between mb-3 pb-2 border-b border-slate-700/30">
        <div className="flex items-center gap-2">
          <Terminal className="w-4 h-4 text-violet-400" />
          <span className="text-xs text-slate-300">
            Steam Price Tracking ({(data.chartData || []).length} points) - {selectedCurrency}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <TrendIcon className={`w-3 h-3 ${getTrendColor()}`} />
          <span className={`text-xs ${getTrendColor()}`}>
            {trendPercentage >= 0 ? '+' : ''}{trendPercentage.toFixed(1)}%
          </span>
        </div>
      </div>

      {/* Price Info and Legend */}
      <div className="flex justify-between items-start mb-3 text-xs">
        <div className="space-y-1">
          {data.prices?.original && (
            <div>
              <span className="text-slate-400">Original: </span>
              <span className="text-white">{data.prices.original.formatted}</span>
            </div>
          )}
          {data.prices?.allTimeLow && (
            <div>
              <span className="text-slate-400">All-Time Low: </span>
              <span className="text-green-400">{data.prices.allTimeLow.formatted}</span>
            </div>
          )}
          {data.prices?.allTimeHigh && (
            <div>
              <span className="text-slate-400">All-Time High: </span>
              <span className="text-red-400">{data.prices.allTimeHigh.formatted}</span>
            </div>
          )}
        </div>
        
        {/* Legend */}
        <div className="text-xs space-y-1">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-violet-500"></div>
            <span className="text-slate-400">original</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span className="text-slate-400">low</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-red-500"></div>
            <span className="text-slate-400">high</span>
          </div>
        </div>
      </div>

      {/* Chart with terminal styling */}
      <div className="h-32 -mx-2 overflow-visible">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart 
            data={chartData}
            margin={{ top: 5, right: 0, left: 0, bottom: 15 }}
          >
            <CartesianGrid 
              strokeDasharray="1 3" 
              stroke="#475569" 
              opacity={0.3}
            />
            <XAxis 
              dataKey="type"
              stroke="#e2e8f0"
              fontSize={11}
              tick={{ fontSize: 11, fontFamily: 'monospace', fill: '#e2e8f0', dy: 10 }}
              axisLine={false}
              tickLine={false}
              height={20}
              tickFormatter={(value) => {
                const typeLabels = {
                  'original': 'original',
                  'low': 'all-time low',
                  'high': 'all-time high'
                };
                return typeLabels[value] || value;
              }}
            />
            <YAxis 
              stroke="#e2e8f0"
              fontSize={10}
              tick={{ fontSize: 10, fontFamily: 'monospace', fill: '#e2e8f0' }}
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => formatPriceShort(value * 100, currency)}
              domain={['dataMin - 10%', 'dataMax + 15%']}
              tickCount={5}
              width={45}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: '#0f172a',
                border: '1px solid #8b5cf6',
                borderRadius: '6px',
                color: '#f1f5f9',
                fontSize: '11px',
                fontFamily: 'monospace',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              labelStyle={{ color: '#8b5cf6' }}
              labelFormatter={(value) => `// ${value}`}
              formatter={(value: number, name: string, props: any) => {
                const payload = props.payload;
                const typeEmoji = {
                  'original': '💰',
                  'low': '📉',
                  'high': '📈'
                };
                
                const typeLabel = {
                  'original': 'ORIGINAL PRICE',
                  'low': 'ALL-TIME LOW',
                  'high': 'ALL-TIME HIGH'
                };
                
                return [
                  `${payload.priceFormatted} ${typeEmoji[payload.type] || '💰'}`,
                  typeLabel[payload.type] || 'STEAM PRICE'
                ];
              }}
            />
            {/* All-time low reference line */}
            {lowestPrice && (
              <ReferenceLine 
                y={lowestPrice} 
                stroke="#10b981" 
                strokeDasharray="2 2" 
                opacity={0.6}
                label={{ 
                  value: "all-time low", 
                  position: "insideTopRight",
                  style: { fontSize: '10px', fill: '#10b981', fontFamily: 'monospace' }
                }}
              />
            )}
            
            {/* Steam Price Points - Staircase ladder effect */}
            <Line
              type="linear"
              dataKey="price"
              stroke="#8b5cf6"
              strokeWidth={3}
              strokeDasharray="5 5"
              dot={(props: any) => {
                const { cx, cy, payload, index } = props;
                if (!payload) return null;
                
                const dotColors = {
                  'original': '#8b5cf6',
                  'low': '#10b981', 
                  'high': '#ef4444'
                };
                
                return (
                  <circle
                    key={`steam-price-dot-${index}-${payload.type || 'unknown'}`}
                    cx={cx}
                    cy={cy}
                    r={payload.dotSize || 5}
                    fill={dotColors[payload.type] || payload.color}
                    stroke="#0f172a"
                    strokeWidth={2}
                  />
                );
              }}
              activeDot={{ 
                r: 10, 
                stroke: '#8b5cf6', 
                strokeWidth: 3,
                fill: '#a855f7',
                shadow: true
              }}
              connectNulls={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </motion.div>
  );
}