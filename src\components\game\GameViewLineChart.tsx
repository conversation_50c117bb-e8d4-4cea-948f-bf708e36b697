'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Terminal, Activity, ChevronUp, ChevronDown, Minus } from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

interface GameViewData {
  totalViews: number;
  uniqueViews: number;
  views24h: number;
  dailyViews: Array<{
    date: string;
    views: number;
  }>;
}

interface GameViewLineChartProps {
  gameId: string;
  className?: string;
}

export default function GameViewLineChart({ gameId, className = '' }: GameViewLineChartProps) {
  const [data, setData] = useState<GameViewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/games/analytics?gameId=${gameId}&days=7&offset=0`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error || 'Failed to load analytics');
      }
    } catch (err) {
      console.error('Error fetching game analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  }, [gameId]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchAnalytics();
    }, 30000);

    return () => clearInterval(interval);
  }, [fetchAnalytics]);

  // Listen for game view tracking events
  useEffect(() => {
    const handleGameViewTracked = (event: CustomEvent) => {
      if (event.detail.gameId === gameId && event.detail.newView) {
        setTimeout(() => fetchAnalytics(), 1000);
      }
    };

    window.addEventListener('gameViewTracked', handleGameViewTracked as EventListener);
    return () => {
      window.removeEventListener('gameViewTracked', handleGameViewTracked as EventListener);
    };
  }, [gameId, fetchAnalytics]);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'yesterday';
    } else {
      return date.toLocaleDateString('en-US', { weekday: 'short' }).toLowerCase();
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  if (loading && !data) {
    return (
      <motion.div 
        className={`bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.01 }}
      >
        {/* Terminal Header */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-slate-700/30">
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-violet-400" />
            <span className="text-xs text-slate-400">analytics.loading()</span>
          </div>
          <div className="flex gap-1">
            <div className="w-2 h-2 rounded-full bg-red-500/60"></div>
            <div className="w-2 h-2 rounded-full bg-yellow-500/60"></div>
            <div className="w-2 h-2 rounded-full bg-green-500/60"></div>
          </div>
        </div>
        
        <div className="animate-pulse space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-violet-400 text-xs">$</span>
            <div className="h-3 bg-slate-700/50 rounded w-32"></div>
          </div>
          <div className="h-20 bg-slate-700/30 rounded border border-slate-700/50"></div>
          <div className="flex justify-between">
            <div className="h-2 bg-slate-700/50 rounded w-16"></div>
            <div className="h-2 bg-slate-700/50 rounded w-12"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  if (error || !data) {
    return (
      <motion.div 
        className={`bg-slate-800/40 border border-red-500/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.01 }}
      >
        {/* Terminal Header */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-red-500/20">
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-red-400" />
            <span className="text-xs text-slate-400">analytics.error()</span>
          </div>
          <div className="flex gap-1">
            <div className="w-2 h-2 rounded-full bg-red-500"></div>
            <div className="w-2 h-2 rounded-full bg-slate-600"></div>
            <div className="w-2 h-2 rounded-full bg-slate-600"></div>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-red-400 text-xs">!</span>
            <span className="text-red-400 text-xs">Error:</span>
            <span className="text-slate-300 text-xs">analytics.fetch() failed</span>
          </div>
          <div className="text-xs text-slate-500">
            // Unable to retrieve view analytics data
          </div>
        </div>
      </motion.div>
    );
  }

  // Calculate trend (comparing latest day with previous day)
  const latestViews = data.dailyViews[0]?.views || 0;
  const previousViews = data.dailyViews[1]?.views || 0;
  const trendPercentage = previousViews > 0 ? ((latestViews - previousViews) / previousViews) * 100 : 0;
  
  const getTrendIcon = () => {
    if (Math.abs(trendPercentage) < 0.1) return Minus;
    return trendPercentage >= 0 ? ChevronUp : ChevronDown;
  };
  
  const getTrendColor = () => {
    if (Math.abs(trendPercentage) < 0.1) return 'text-slate-400';
    return trendPercentage >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const getTrendSymbol = () => {
    if (Math.abs(trendPercentage) < 0.1) return '~';
    return trendPercentage >= 0 ? '▲' : '▼';
  };

  const TrendIcon = getTrendIcon();

  // Prepare chart data - reverse to show chronological order (oldest to newest)
  const chartData = data.dailyViews.slice().reverse().map(day => ({
    ...day,
    formattedDate: formatDate(day.date)
  }));

  return (
    <motion.div
      className={`bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200 font-mono h-full flex flex-col ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.01 }}
    >
      {/* Terminal Header */}
      <div className="flex items-center justify-between mb-3 pb-2 border-b border-slate-700/30 flex-shrink-0">
        <div className="flex items-center gap-2">
          <Terminal className="w-4 h-4 text-violet-400" />
          <span className="text-xs text-slate-300">Last 7 Days Views: {formatNumber(data.totalViews)}</span>
        </div>
      </div>

      {/* Chart with terminal styling */}
      <div className="flex-1 -mx-2 overflow-visible min-h-0">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 5, right: 0, left: 0, bottom: 15 }}
          >
            <CartesianGrid 
              strokeDasharray="1 3" 
              stroke="#475569" 
              opacity={0.3}
            />
            <XAxis 
              dataKey="formattedDate"
              stroke="#e2e8f0"
              fontSize={11}
              tick={{ fontSize: 11, fontFamily: 'monospace', fill: '#e2e8f0', dy: 10 }}
              axisLine={false}
              tickLine={false}
              height={20}
            />
            <YAxis 
              stroke="#e2e8f0"
              fontSize={10}
              tick={{ fontSize: 10, fontFamily: 'monospace', fill: '#e2e8f0' }}
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => {
                if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                if (value >= 1000) return `${(value / 1000).toFixed(0)}K`;
                return value.toString();
              }}
              domain={['dataMin - 5%', 'dataMax + 5%']}
              tickCount={4}
              width={30}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: '#0f172a',
                border: '1px solid #8b5cf6',
                borderRadius: '6px',
                color: '#f1f5f9',
                fontSize: '11px',
                fontFamily: 'monospace',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              labelStyle={{ color: '#8b5cf6' }}
              labelFormatter={(value) => `// ${value}`}
              formatter={(value: number) => [
                `${value.toLocaleString()}`,
                'views'
              ]}
            />
            <Line
              type="monotone"
              dataKey="views"
              stroke="#8b5cf6"
              strokeWidth={2}
              dot={{ 
                fill: '#8b5cf6', 
                strokeWidth: 1, 
                r: 2,
                stroke: '#0f172a'
              }}
              activeDot={{ 
                r: 4, 
                stroke: '#8b5cf6', 
                strokeWidth: 2,
                fill: '#a855f7',
                shadow: true
              }}
              connectNulls={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

    </motion.div>
  );
}