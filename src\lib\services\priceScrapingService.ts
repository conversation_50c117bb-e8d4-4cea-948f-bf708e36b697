import puppeteer from 'puppeteer';
import { createServerClient } from '@/lib/supabase/server';
import { steamApiService } from './steamApiService';
import { kinguinApiService, type KinguinGamePrice } from './kinguinApiService';
import { cjApiService, CJGamePrice } from './cjApiService';
import { DEFAULT_REGION } from '@/lib/constants/steamRegions';

interface PriceData {
  store_name: string;
  price: string;
  original_price?: string;
  discount_percentage?: number;
  store_url: string;
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  region_code: string; // Added for multi-region support
  affiliate_url?: string; // Added for affiliate tracking
}

interface ScrapingResult {
  success: boolean;
  data?: PriceData[];
  error?: string;
}

export class PriceScrapingService {
  private browser: any = null;

  async init() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true, // Use headless mode
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled',
          '--no-first-run',
          '--disable-default-apps',
          '--disable-dev-shm-usage',
          '--window-size=1920,1080'
        ],
        ignoreDefaultArgs: ['--enable-automation'],
        defaultViewport: { width: 1920, height: 1080 }
      });
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async scrapeSteam(gameName: string, regionCode: string = DEFAULT_REGION): Promise<PriceData | null> {
    try {
      console.log(`🎮 Steam API: Fetching price for "${gameName}" in region ${regionCode.toUpperCase()}`);
      
      // Use Steam API instead of scraping
      const steamPriceData = await steamApiService.getGamePrice(gameName, regionCode);
      
      if (!steamPriceData) {
        console.log(`❌ Steam API: No price data found for "${gameName}" in region ${regionCode}`);
        return null;
      }

      console.log(`✅ Steam API: Found price for "${gameName}" in ${regionCode.toUpperCase()}: ${steamPriceData.price}`);
      
      return {
        store_name: steamPriceData.store_name,
        price: steamPriceData.price,
        original_price: steamPriceData.original_price,
        discount_percentage: steamPriceData.discount_percentage,
        store_url: steamPriceData.store_url,
        currency: steamPriceData.currency,
        availability: steamPriceData.availability,
        region_code: steamPriceData.region_code
      };
    } catch (error) {
      console.error(`Erro ao buscar preço no Steam via API para região ${regionCode}:`, error);
      return null;
    }
  }

  /**
   * Get Steam prices for multiple regions
   */
  async scrapeSteamMultiRegion(gameName: string, regionCodes: string[]): Promise<PriceData[]> {
    try {
      console.log(`🌍 Steam: Fetching "${gameName}" prices in ${regionCodes.length} regions`);
      
      const steamPrices = await steamApiService.getGamePriceMultiRegion(gameName, regionCodes);
      
      return steamPrices.map(steamPrice => ({
        store_name: steamPrice.store_name,
        price: steamPrice.price,
        original_price: steamPrice.original_price,
        discount_percentage: steamPrice.discount_percentage,
        store_url: steamPrice.store_url,
        currency: steamPrice.currency,
        availability: steamPrice.availability,
        region_code: steamPrice.region_code
      }));
    } catch (error) {
      console.error('Erro ao buscar preços Steam multi-região:', error);
      return [];
    }
  }

  async scrapeNuuvem(gameName: string, regionCode: string = 'br'): Promise<PriceData | null> {
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://www.nuuvem.com/catalog/search/${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Aguardar carregamento com seletores múltiplos e timeout maior
      try {
        await page.waitForSelector('.product-card, .product-item, .game-card, [data-testid="product"], .card', { timeout: 15000 });
      } catch (timeoutError) {
        console.log('⚠️ Nuuvem: Nenhum produto encontrado ou página não carregou completamente');
        await page.close();
        return null;
      }

      const gameData = await page.evaluate(() => {
        // Tentar múltiplos seletores para encontrar produtos
        const firstProduct = document.querySelector('.product-card') || 
                            document.querySelector('.product-item') || 
                            document.querySelector('.game-card') || 
                            document.querySelector('[data-testid="product"]') ||
                            document.querySelector('.card');
        
        if (!firstProduct) return null;

        // Tentar múltiplos seletores para título
        const titleElement = firstProduct.querySelector('.product-title') ||
                            firstProduct.querySelector('.title') ||
                            firstProduct.querySelector('.game-title') ||
                            firstProduct.querySelector('h3') ||
                            firstProduct.querySelector('h2');
        
        // Tentar múltiplos seletores para preço
        const priceElement = firstProduct.querySelector('.product-price') ||
                            firstProduct.querySelector('.price') ||
                            firstProduct.querySelector('.cost') ||
                            firstProduct.querySelector('[class*="price"]');
        
        const linkElement = firstProduct.querySelector('a');

        if (!titleElement || !priceElement || !linkElement) return null;

        const title = titleElement.textContent?.trim();
        const priceText = priceElement.textContent?.trim();
        const href = linkElement.getAttribute('href');

        // Extrair preços
        const priceMatch = priceText?.match(/R\$\s*[\d,]+/g);
        let currentPrice = '';
        let originalPrice = '';

        if (priceMatch) {
          currentPrice = priceMatch[priceMatch.length - 1];
          if (priceMatch.length > 1) {
            originalPrice = priceMatch[0];
          }
        }

        return {
          title,
          currentPrice,
          originalPrice,
          url: href ? `https://www.nuuvem.com${href}` : ''
        };
      });

      await page.close();

      if (!gameData || !gameData.currentPrice) return null;

      // Calcular desconto
      let discountPercentage = 0;
      if (gameData.originalPrice && gameData.currentPrice) {
        const original = parseFloat(gameData.originalPrice.replace(/[R$\s,]/g, ''));
        const current = parseFloat(gameData.currentPrice.replace(/[R$\s,]/g, ''));
        discountPercentage = Math.round(((original - current) / original) * 100);
      }

      return {
        store_name: 'Nuuvem',
        price: gameData.currentPrice,
        original_price: gameData.originalPrice || undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: gameData.url,
        currency: 'BRL',
        availability: 'available',
        region_code: regionCode // Nuuvem is Brazil-only, so always 'br'
      };
    } catch (error) {
      console.error('Erro ao fazer scraping da Nuuvem:', error);
      return null;
    }
  }

  async scrapeEpicGames(gameName: string, regionCode: string = 'us'): Promise<PriceData | null> {
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://store.epicgames.com/en-US/browse?q=${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Aguardar carregamento
      await new Promise(resolve => setTimeout(resolve, 3000));

      const gameData = await page.evaluate(() => {
        // Epic Games usa React, então precisamos aguardar o carregamento
        const productCard = document.querySelector('[data-testid="offer-card"]');
        if (!productCard) return null;

        const titleElement = productCard.querySelector('[data-testid="offer-title-info-title"]');
        const priceElement = productCard.querySelector('[data-testid="price-display"]');
        const linkElement = productCard.querySelector('a');

        if (!titleElement || !linkElement) return null;

        const title = titleElement.textContent?.trim();
        const priceText = priceElement?.textContent?.trim();
        const href = linkElement.getAttribute('href');

        // Epic Games pode ter jogos gratuitos
        let currentPrice = '';
        let originalPrice = '';
        let isFree = false;

        if (priceText) {
          if (priceText.toLowerCase().includes('free')) {
            currentPrice = 'Free';
            isFree = true;
          } else {
            // Tentar extrair preços USD
            const priceMatch = priceText.match(/\$[\d.]+/g);
            if (priceMatch) {
              currentPrice = priceMatch[priceMatch.length - 1];
              if (priceMatch.length > 1) {
                originalPrice = priceMatch[0];
              }
            }
          }
        }

        return {
          title,
          currentPrice,
          originalPrice,
          isFree,
          url: href ? `https://store.epicgames.com${href}` : ''
        };
      });

      await page.close();

      if (!gameData || (!gameData.currentPrice && !gameData.isFree)) return null;

      // Calcular desconto
      let discountPercentage = 0;
      if (gameData.originalPrice && gameData.currentPrice && !gameData.isFree) {
        const original = parseFloat(gameData.originalPrice.replace(/[$]/g, ''));
        const current = parseFloat(gameData.currentPrice.replace(/[$]/g, ''));
        discountPercentage = Math.round(((original - current) / original) * 100);
      }

      return {
        store_name: 'Epic Games',
        price: gameData.isFree ? 'Gratuito' : gameData.currentPrice,
        original_price: gameData.originalPrice || undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: gameData.url,
        currency: gameData.isFree ? 'USD' : 'USD',
        availability: 'available',
        region_code: regionCode // Epic Games typically uses USD/US region
      };
    } catch (error) {
      console.error('Erro ao fazer scraping do Epic Games:', error);
      return null;
    }
  }

  async scrapeInstantGaming(gameName: string, regionCode: string = 'us'): Promise<PriceData | null> {
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://www.instant-gaming.com/en/search/?q=${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Aguardar carregamento específico do Instant Gaming
      await new Promise(resolve => setTimeout(resolve, 2000));

      const gameData = await page.evaluate(() => {
        const firstProduct = document.querySelector('.item');
        if (!firstProduct) return null;

        const titleElement = firstProduct.querySelector('.title');
        const priceElement = firstProduct.querySelector('.price');
        const discountElement = firstProduct.querySelector('.discount');
        const linkElement = firstProduct.querySelector('a');

        if (!titleElement || !priceElement || !linkElement) return null;

        const title = titleElement.textContent?.trim();
        const priceText = priceElement.textContent?.trim();
        const discountText = discountElement?.textContent?.trim();
        const href = linkElement.getAttribute('href');

        // Instant Gaming sempre tem preços em USD (ou EUR dependendo da região)
        const priceMatch = priceText?.match(/\$[\d.]+/g) || priceText?.match(/€[\d.]+/g);
        let currentPrice = '';
        let discountPercentage = 0;

        if (priceMatch) {
          currentPrice = priceMatch[0];
        }

        // Extrair desconto se disponível
        if (discountText) {
          const discountMatch = discountText.match(/(\d+)%/);
          if (discountMatch) {
            discountPercentage = parseInt(discountMatch[1]);
          }
        }

        return {
          title,
          currentPrice,
          discountPercentage,
          url: href ? `https://www.instant-gaming.com${href}` : ''
        };
      });

      await page.close();

      if (!gameData || !gameData.currentPrice) return null;

      return {
        store_name: 'Instant Gaming',
        price: gameData.currentPrice,
        original_price: undefined, // Instant Gaming não mostra preço original claramente
        discount_percentage: gameData.discountPercentage > 0 ? gameData.discountPercentage : undefined,
        store_url: gameData.url,
        currency: gameData.currentPrice.includes('€') ? 'EUR' : 'USD',
        availability: 'available',
        region_code: regionCode
      };
    } catch (error) {
      console.error('Erro ao fazer scraping do Instant Gaming:', error);
      return null;
    }
  }

  async scrapeKinguin(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀🚀🚀 KINGUIN SCRAPER DEFINITELY CALLED for "${gameName}" in region ${regionCode} 🚀🚀🚀`);
    
    try {
      console.log(`🔧 Checking if Kinguin is configured...`);
      
      // Check if Kinguin service is configured
      if (!kinguinApiService.isConfigured()) {
        console.error(`❌❌❌ KINGUIN NOT CONFIGURED - MISSING API KEY OR AFFILIATE ID ❌❌❌`);
        console.error(`Current config:`, {
          hasApiKey: !!process.env.KINGUIN_API_KEY,
          hasAffiliateId: !!process.env.KINGUIN_AFFILIATE_ID,
          apiKeyLength: process.env.KINGUIN_API_KEY?.length || 0,
          affiliateId: process.env.KINGUIN_AFFILIATE_ID
        });
        return null;
      }
      
      console.log(`✅ Kinguin is configured, calling getGamePrice for region ${regionCode}...`);
      
      // Use Kinguin API 
      const kinguinData = await kinguinApiService.getGamePrice(gameName, regionCode);
      
      console.log(`🔍 Kinguin getGamePrice returned for region ${regionCode}:`, kinguinData);
      
      if (!kinguinData) {
        console.error(`❌ Kinguin API returned null for "${gameName}" in region ${regionCode}`);
        return null;
      }

      console.log(`✅ Kinguin found price for region ${regionCode}: ${kinguinData.currency} ${kinguinData.price}`);
      
      // Note: Kinguin handles its own currency conversion internally
      // Other stores will use their native regional pricing without conversion
      const priceData = {
        store_name: 'Kinguin',
        price: kinguinApiService.formatPrice(kinguinData.price, kinguinData.currency),
        original_price: kinguinData.originalPrice ? 
          kinguinApiService.formatPrice(kinguinData.originalPrice, kinguinData.currency) : undefined,
        discount_percentage: kinguinData.discountPercentage,
        store_url: kinguinData.storeUrl,
        affiliate_url: kinguinData.affiliateUrl,
        currency: kinguinData.currency,
        availability: kinguinData.availability,
        region_code: regionCode
      };
      
      console.log(`🎯 FINAL KINGUIN PRICE DATA for region ${regionCode}:`, priceData);
      
      return priceData;
      
    } catch (error) {
      console.error(`💥💥💥 KINGUIN SCRAPER ERROR for region ${regionCode}:`, error);
      console.error(`Error details:`, {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return null;
    }
  }

  /**
   * NEW: Scrape Kinguin prices for ALL supported currencies at once
   * This method fetches and returns price data for all currencies simultaneously
   */
  async scrapeKinguinAllCurrencies(gameName: string): Promise<PriceData[]> {
    console.log(`🌍🚀 KINGUIN MULTI-CURRENCY SCRAPER for "${gameName}" - fetching ALL currencies 🚀🌍`);
    
    try {
      // Check if Kinguin service is configured
      if (!kinguinApiService.isConfigured()) {
        console.error(`❌ KINGUIN NOT CONFIGURED - MISSING API KEY OR AFFILIATE ID`);
        return [];
      }
      
      console.log(`✅ Kinguin configured, fetching prices in ALL supported currencies...`);
      
      // Get prices for all currencies from Kinguin API
      const allCurrencyPrices = await kinguinApiService.getGamePricesAllCurrencies(gameName);
      
      if (!allCurrencyPrices || allCurrencyPrices.length === 0) {
        console.error(`❌ Kinguin multi-currency API returned no prices for "${gameName}"`);
        return [];
      }

      console.log(`✅ Kinguin found prices in ${allCurrencyPrices.length} currencies for "${gameName}"`);
      
      // Convert to PriceData format for each currency
      const priceDataArray: PriceData[] = allCurrencyPrices.map(kinguinData => {
        const priceData = {
          store_name: 'Kinguin',
          price: kinguinApiService.formatPrice(kinguinData.price, kinguinData.currency),
          original_price: kinguinData.originalPrice ? 
            kinguinApiService.formatPrice(kinguinData.originalPrice, kinguinData.currency) : undefined,
          discount_percentage: kinguinData.discountPercentage,
          store_url: kinguinData.storeUrl,
          affiliate_url: kinguinData.affiliateUrl,
          currency: kinguinData.currency,
          availability: kinguinData.availability,
          region_code: kinguinData.region
        };
        
        console.log(`💰 Kinguin ${kinguinData.currency} (${kinguinData.region}): ${priceData.price}`);
        return priceData;
      });
      
      console.log(`🎯 KINGUIN MULTI-CURRENCY COMPLETE: Generated ${priceDataArray.length} price records`);
      
      return priceDataArray;
      
    } catch (error) {
      console.error(`💥 KINGUIN MULTI-CURRENCY SCRAPER ERROR:`, error);
      console.error(`Error details:`, {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return [];
    }
  }

  /**
   * Scrape Fanatical prices via CJ API
   */
  async scrapeFanatical(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀 FANATICAL (CJ) SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      if (!cjApiService.isConfigured()) {
        console.error(`❌ CJ API not configured - missing credentials`);
        return null;
      }
      
      const fanaticalData = await cjApiService.getFanaticalPrice(gameName, regionCode);
      
      if (!fanaticalData) {
        console.error(`❌ CJ/Fanatical API returned null for "${gameName}"`);
        return null;
      }

      console.log(`✅ CJ/Fanatical found price: ${fanaticalData.currency} ${fanaticalData.price}`);
      
      return {
        store_name: 'Fanatical',
        price: cjApiService.formatPrice(fanaticalData.price, fanaticalData.currency),
        original_price: fanaticalData.original_price ? 
          cjApiService.formatPrice(fanaticalData.original_price, fanaticalData.currency) : undefined,
        discount_percentage: fanaticalData.discount_percentage,
        store_url: fanaticalData.store_url,
        affiliate_url: fanaticalData.affiliate_url,
        currency: fanaticalData.currency,
        availability: fanaticalData.availability,
        region_code: regionCode
      };
    } catch (error) {
      console.error(`💥 FANATICAL (CJ) SCRAPER ERROR:`, error);
      return null;
    }
  }

  /**
   * Scrape Gamivo prices via CJ API
   */
  async scrapeGamivo(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀 GAMIVO (CJ) SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      if (!cjApiService.isConfigured()) {
        console.error(`❌ CJ API not configured - missing credentials`);
        return null;
      }
      
      const gamivoData = await cjApiService.getGamivoPrice(gameName, regionCode);
      
      if (!gamivoData) {
        console.error(`❌ CJ/Gamivo API returned null for "${gameName}"`);
        return null;
      }

      console.log(`✅ CJ/Gamivo found price: ${gamivoData.currency} ${gamivoData.price}`);
      
      return {
        store_name: 'Gamivo',
        price: cjApiService.formatPrice(gamivoData.price, gamivoData.currency),
        original_price: gamivoData.original_price ? 
          cjApiService.formatPrice(gamivoData.original_price, gamivoData.currency) : undefined,
        discount_percentage: gamivoData.discount_percentage,
        store_url: gamivoData.store_url,
        affiliate_url: gamivoData.affiliate_url,
        currency: gamivoData.currency,
        availability: gamivoData.availability,
        region_code: regionCode
      };
    } catch (error) {
      console.error(`💥 GAMIVO (CJ) SCRAPER ERROR:`, error);
      return null;
    }
  }

  /**
   * Scrape all CJ gaming stores (Fanatical, Humble Bundle, GamesPlanet, etc.)
   */
  async scrapeCJGamingStores(gameName: string, regionCode: string = 'global'): Promise<PriceData[]> {
    console.log(`🌍 CJ GAMING STORES SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      if (!cjApiService.isConfigured()) {
        console.error(`❌ CJ API not configured - missing credentials`);
        return [];
      }
      
      const cjPrices = await cjApiService.getAllGamingPrices(gameName, regionCode);
      
      if (!cjPrices || cjPrices.length === 0) {
        console.error(`❌ CJ API returned no gaming prices for "${gameName}"`);
        return [];
      }

      console.log(`✅ CJ found ${cjPrices.length} gaming store prices for "${gameName}"`);
      
      // Convert to PriceData format
      const priceDataArray: PriceData[] = cjPrices.map((cjData: CJGamePrice) => ({
        store_name: cjData.store_name,
        price: cjApiService.formatPrice(cjData.price, cjData.currency),
        original_price: cjData.original_price ? 
          cjApiService.formatPrice(cjData.original_price, cjData.currency) : undefined,
        discount_percentage: cjData.discount_percentage,
        store_url: cjData.store_url,
        affiliate_url: cjData.affiliate_url,
        currency: cjData.currency,
        availability: cjData.availability,
        region_code: regionCode
      }));

      priceDataArray.forEach(priceData => {
        console.log(`💰 CJ/${priceData.store_name}: ${priceData.price}`);
      });
      
      return priceDataArray;
      
    } catch (error) {
      console.error(`💥 CJ GAMING STORES SCRAPER ERROR:`, error);
      return [];
    }
  }

  /**
   * Scrape prices for a game in a specific region
   */
  async scrapePricesForGame(gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<ScrapingResult> {
    try {
      await this.init();

      console.log(`🔍 Starting price scraping for "${gameName}" in region ${regionCode.toUpperCase()}`);

      // Only Steam supports true multi-region pricing via API
      // Other stores are region-specific by nature
      const scrapingPromises = [
        this.scrapeSteam(gameName, regionCode), // Uses Steam API with region support
        this.scrapeKinguin(gameName, regionCode), // Pass region so Kinguin can map to currency
        this.scrapeFanatical(gameName, regionCode), // CJ API with Fanatical
        this.scrapeGamivo(gameName, regionCode), // CJ API with Gamivo
        // Other stores are always scraped for their default regions
        ...(regionCode === 'br' ? [this.scrapeNuuvem(gameName, regionCode)] : []),
        ...(regionCode === 'us' || regionCode === 'de' ? [
          this.scrapeEpicGames(gameName, regionCode),
          this.scrapeInstantGaming(gameName, regionCode)
        ] : [])
      ];

      console.log(`📊 SCRAPING STORES: Will scrape ${scrapingPromises.length} stores for "${gameName}" in region ${regionCode}:`, {
        stores: ['Steam', 'Kinguin', 'Fanatical (CJ)', 'Gamivo', ...(regionCode === 'br' ? ['Nuuvem'] : []), ...(regionCode === 'us' || regionCode === 'de' ? ['Epic Games', 'Instant Gaming'] : [])],
        region: regionCode
      });

      const results = await Promise.allSettled(scrapingPromises);
      const successfulPrices: PriceData[] = [];

      results.forEach((result, index) => {
        const storeNames = ['Steam', 'Kinguin', 'Fanatical (CJ)', 'Gamivo', ...(regionCode === 'br' ? ['Nuuvem'] : []), ...(regionCode === 'us' || regionCode === 'de' ? ['Epic Games', 'Instant Gaming'] : [])];
        const storeName = storeNames[index] || `Store ${index + 1}`;
        
        if (result.status === 'fulfilled' && result.value) {
          successfulPrices.push(result.value);
          console.log(`✅ ${storeName}: Found price ${result.value.price} from ${result.value.store_name}`);
        } else if (result.status === 'fulfilled' && !result.value) {
          console.log(`⚠️ ${storeName}: Returned null (no price found)`);
        } else if (result.status === 'rejected') {
          console.error(`❌ ${storeName}: Scraping failed:`, result.reason);
        }
      });

      console.log(`🎯 Scraping completed for "${gameName}" in region ${regionCode}: ${successfulPrices.length} prices found`);

      return {
        success: successfulPrices.length > 0,
        data: successfulPrices,
        error: successfulPrices.length === 0 ? `Nenhum preço encontrado para a região ${regionCode.toUpperCase()}` : undefined
      };
    } catch (error) {
      console.error(`Erro geral no scraping para região ${regionCode}:`, error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    } finally {
      await this.close();
    }
  }

  /**
   * Scrape Steam prices for multiple regions
   */
  async scrapeSteamPricesMultiRegion(gameName: string, regionCodes: string[]): Promise<ScrapingResult> {
    try {
      console.log(`🌍 Starting multi-region Steam price scraping for "${gameName}" in ${regionCodes.length} regions`);

      const steamPrices = await this.scrapeSteamMultiRegion(gameName, regionCodes);

      return {
        success: steamPrices.length > 0,
        data: steamPrices,
        error: steamPrices.length === 0 ? 'Nenhum preço Steam encontrado nas regiões especificadas' : undefined
      };
    } catch (error) {
      console.error('Erro no scraping Steam multi-região:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Update game prices in database for a specific region
   */
  async updateGamePrices(gameId: string, gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<boolean> {
    try {
      console.log(`🔄 Updating prices for game ${gameId} (${gameName}) in region ${regionCode}`);

      const scrapingResult = await this.scrapePricesForGame(gameName, regionCode, steamAppId);
      
      if (!scrapingResult.success || !scrapingResult.data) {
        console.log(`⚠️ No prices found for ${gameName} in region ${regionCode}`);
        return false;
      }

             const supabase = await createServerClient();

       // Insert or update prices for this region
       for (const priceData of scrapingResult.data) {
         const { error } = await supabase
          .from('game_prices')
          .upsert({
            game_id: gameId,
            store_name: priceData.store_name,
            region_code: priceData.region_code,
            price: priceData.price,
            original_price: priceData.original_price,
            discount_percentage: priceData.discount_percentage,
            store_url: priceData.store_url,
            affiliate_url: priceData.affiliate_url, // Add affiliate tracking
            currency: priceData.currency,
            availability: priceData.availability,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'game_id,store_name,region_code'
          });

        if (error) {
          console.error(`❌ Error updating ${priceData.store_name} price for region ${regionCode}:`, error);
        } else {
          console.log(`✅ Updated ${priceData.store_name} price: ${priceData.price} (${priceData.currency}) for region ${regionCode}`);
        }
      }

      return true;
    } catch (error) {
      console.error(`Error updating game prices for region ${regionCode}:`, error);
      return false;
    }
  }

  /**
   * NEW: Update game prices with Kinguin multi-currency support
   * This method updates prices for all stores, with Kinguin fetching ALL currencies at once
   */
  async updateGamePricesWithMultiCurrency(gameId: string, gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<boolean> {
    try {
      console.log(`🌍 Updating prices with multi-currency for game ${gameId} (${gameName}) in region ${regionCode}`);

      const supabase = await createServerClient();
      let hasAnyPrices = false;

      // 1. Get regular Steam prices for the specific region
      console.log(`🎮 Fetching Steam prices for region ${regionCode}...`);
      await this.init();
      const steamPriceData = await this.scrapeSteam(gameName, regionCode);
      
      if (steamPriceData) {
        const { error } = await supabase
          .from('game_prices')
          .upsert({
            game_id: gameId,
            store_name: steamPriceData.store_name,
            region_code: steamPriceData.region_code,
            price: steamPriceData.price,
            original_price: steamPriceData.original_price,
            discount_percentage: steamPriceData.discount_percentage,
            store_url: steamPriceData.store_url,
            affiliate_url: steamPriceData.affiliate_url,
            currency: steamPriceData.currency,
            availability: steamPriceData.availability,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'game_id,store_name,region_code'
          });

        if (error) {
          console.error(`❌ Error updating Steam price:`, error);
        } else {
          console.log(`✅ Updated Steam price: ${steamPriceData.price} (${steamPriceData.currency}) for region ${regionCode}`);
          hasAnyPrices = true;
        }
      }

      // 2. Get Kinguin prices for ALL currencies at once
      console.log(`🌍 Fetching Kinguin prices for ALL currencies...`);
      const kinguinAllCurrencies = await this.scrapeKinguinAllCurrencies(gameName);
      
      if (kinguinAllCurrencies && kinguinAllCurrencies.length > 0) {
        console.log(`💰 Updating ${kinguinAllCurrencies.length} Kinguin currency records in database...`);
        
        // Insert/update all Kinguin currency records
        for (const priceData of kinguinAllCurrencies) {
          const { error } = await supabase
            .from('game_prices')
            .upsert({
              game_id: gameId,
              store_name: priceData.store_name,
              region_code: priceData.region_code,
              price: priceData.price,
              original_price: priceData.original_price,
              discount_percentage: priceData.discount_percentage,
              store_url: priceData.store_url,
              affiliate_url: priceData.affiliate_url,
              currency: priceData.currency,
              availability: priceData.availability,
              last_updated: new Date().toISOString()
            }, {
              onConflict: 'game_id,store_name,region_code'
            });

          if (error) {
            console.error(`❌ Error updating Kinguin ${priceData.currency} price:`, error);
          } else {
            console.log(`✅ Updated Kinguin price: ${priceData.price} (${priceData.currency}) for region ${priceData.region_code}`);
            hasAnyPrices = true;
          }
        }
      }

      // 3. Get other regional stores if appropriate
      if (regionCode === 'br') {
        console.log(`🇧🇷 Fetching Nuuvem prices for Brazil...`);
        const nuuvemPriceData = await this.scrapeNuuvem(gameName, regionCode);
        
        if (nuuvemPriceData) {
          const { error } = await supabase
            .from('game_prices')
            .upsert({
              game_id: gameId,
              store_name: nuuvemPriceData.store_name,
              region_code: nuuvemPriceData.region_code,
              price: nuuvemPriceData.price,
              original_price: nuuvemPriceData.original_price,
              discount_percentage: nuuvemPriceData.discount_percentage,
              store_url: nuuvemPriceData.store_url,
              affiliate_url: nuuvemPriceData.affiliate_url,
              currency: nuuvemPriceData.currency,
              availability: nuuvemPriceData.availability,
              last_updated: new Date().toISOString()
            }, {
              onConflict: 'game_id,store_name,region_code'
            });

          if (error) {
            console.error(`❌ Error updating Nuuvem price:`, error);
          } else {
            console.log(`✅ Updated Nuuvem price: ${nuuvemPriceData.price} (${nuuvemPriceData.currency}) for region ${regionCode}`);
            hasAnyPrices = true;
          }
        }
      }

      if (regionCode === 'us' || regionCode === 'de') {
        console.log(`🌐 Fetching Epic Games and Instant Gaming prices...`);
        
        const epicPriceData = await this.scrapeEpicGames(gameName, regionCode);
        const instantGamingPriceData = await this.scrapeInstantGaming(gameName, regionCode);
        
        const otherStorePrices = [epicPriceData, instantGamingPriceData].filter(Boolean);
        
        for (const priceData of otherStorePrices) {
          if (priceData) {
            const { error } = await supabase
              .from('game_prices')
              .upsert({
                game_id: gameId,
                store_name: priceData.store_name,
                region_code: priceData.region_code,
                price: priceData.price,
                original_price: priceData.original_price,
                discount_percentage: priceData.discount_percentage,
                store_url: priceData.store_url,
                affiliate_url: priceData.affiliate_url,
                currency: priceData.currency,
                availability: priceData.availability,
                last_updated: new Date().toISOString()
              }, {
                onConflict: 'game_id,store_name,region_code'
              });

            if (error) {
              console.error(`❌ Error updating ${priceData.store_name} price:`, error);
            } else {
              console.log(`✅ Updated ${priceData.store_name} price: ${priceData.price} (${priceData.currency}) for region ${regionCode}`);
              hasAnyPrices = true;
            }
          }
        }
      }

      await this.close();

      if (hasAnyPrices) {
        console.log(`🎯 Multi-currency price update completed successfully for "${gameName}"`);
      } else {
        console.log(`⚠️ No prices found for "${gameName}" in any store or currency`);
      }

      return hasAnyPrices;
      
    } catch (error) {
      console.error(`Error updating game prices with multi-currency:`, error);
      await this.close();
      return false;
    }
  }

  /**
   * Update Steam prices for multiple regions
   */
  async updateGamePricesMultiRegion(gameId: string, gameName: string, regionCodes: string[]): Promise<boolean> {
    try {
      console.log(`🌍 Updating Steam prices for game ${gameId} (${gameName}) in ${regionCodes.length} regions`);

      const scrapingResult = await this.scrapeSteamPricesMultiRegion(gameName, regionCodes);
      
      if (!scrapingResult.success || !scrapingResult.data) {
        console.log(`⚠️ No Steam prices found for ${gameName} in specified regions`);
        return false;
      }

             const supabase = await createServerClient();

       // Insert or update prices for all regions
       for (const priceData of scrapingResult.data) {
         const { error } = await supabase
          .from('game_prices')
          .upsert({
            game_id: gameId,
            store_name: priceData.store_name,
            region_code: priceData.region_code,
            price: priceData.price,
            original_price: priceData.original_price,
            discount_percentage: priceData.discount_percentage,
            store_url: priceData.store_url,
            affiliate_url: priceData.affiliate_url, // Add affiliate tracking
            currency: priceData.currency,
            availability: priceData.availability,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'game_id,store_name,region_code'
          });

        if (error) {
          console.error(`❌ Error updating ${priceData.store_name} price for region ${priceData.region_code}:`, error);
        } else {
          console.log(`✅ Updated ${priceData.store_name} price: ${priceData.price} (${priceData.currency}) for region ${priceData.region_code}`);
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating multi-region game prices:', error);
      return false;
    }
  }

  /**
   * Get all available prices for a game from various sources
   */
  async getAllPrices(gameName: string, regionCode: string = 'global'): Promise<PriceData[]> {
    console.log(`🚀 SCRAPING ALL PRICES: Searching for "${gameName}" in region ${regionCode}`);
    
    const allPrices: PriceData[] = [];
    const scrapingPromises: Promise<PriceData | null>[] = [];

    // Add scraping for all stores, including Gamivo
    scrapingPromises.push(this.scrapeFanatical(gameName, regionCode));
    scrapingPromises.push(this.scrapeGamivo(gameName, regionCode));
    
    // Add other scrapers if they exist
    // scrapingPromises.push(this.scrapeHumbleBundle(gameName, regionCode));
    // scrapingPromises.push(this.scrapeEpicGames(gameName, regionCode));

    try {
      // Wait for all scraping to complete
      const results = await Promise.allSettled(scrapingPromises);
      
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          allPrices.push(result.value);
        } else if (result.status === 'rejected') {
          console.error('❌ Scraping failed:', result.reason);
        }
      }

      // Sort by price (lowest first) - convert string prices to numbers for comparison
      allPrices.sort((a, b) => {
        const priceA = parseFloat(a.price.replace(/[^\d.,]/g, '').replace(',', '.'));
        const priceB = parseFloat(b.price.replace(/[^\d.,]/g, '').replace(',', '.'));
        return priceA - priceB;
      });

      console.log(`✅ TOTAL PRICES FOUND: ${allPrices.length} para "${gameName}"`);
      allPrices.forEach(price => {
        console.log(`💰 ${price.store_name}: ${price.currency} ${price.price}${price.discount_percentage ? ` (${price.discount_percentage}% off)` : ''}`);
      });

      return allPrices;

    } catch (error) {
      console.error('❌ Error in getAllPrices:', error);
      return [];
    }
  }
}

// Legacy functions for backward compatibility
export async function atualizarPrecosJogo(game: { id: string, name: string }, regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  return await scraper.updateGamePrices(game.id, game.name, regionCode);
}

export async function atualizarPrecosJogoMultiRegion(game: { id: string, name: string }, regionCodes: string[]) {
  const scraper = new PriceScrapingService();
  return await scraper.updateGamePricesMultiRegion(game.id, game.name, regionCodes);
}

export async function atualizarPrecosEmLote(games: { id: string, name: string }[], regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  const results = [];
  
  for (const game of games) {
    const result = await scraper.updateGamePrices(game.id, game.name, regionCode);
    results.push(result);
  }
  
  return results;
}

// NEW: Multi-currency update functions
export async function atualizarPrecosJogoComMultiCurrency(game: { id: string, name: string }, regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  return await scraper.updateGamePricesWithMultiCurrency(game.id, game.name, regionCode);
}

export async function atualizarPrecosEmLoteComMultiCurrency(games: { id: string, name: string }[], regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  const results = [];
  
  for (const game of games) {
    const result = await scraper.updateGamePricesWithMultiCurrency(game.id, game.name, regionCode);
    results.push(result);
  }
  
  return results;
} 