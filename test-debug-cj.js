/**
 * Debug script to test CJ API directly
 * This will help identify why Gamivo/Fanatical aren't showing up
 */

console.log('🔍 DEBUG: Testing CJ API functions directly...\n');

// Simulate the CJ API service behavior
function simulateCJTest() {
    console.log('📋 DEBUG CHECKLIST:');
    console.log('');
    
    console.log('1. 🔧 Check if CJ_FORCE_REAL_DATA is set in .env.local');
    console.log('   Expected: CJ_FORCE_REAL_DATA=true');
    console.log('   If false/missing: Will return mock data only');
    console.log('');
    
    console.log('2. 🔑 Check if CJ API credentials are set:');
    console.log('   - CJ_DEVELOPER_KEY=your_key');
    console.log('   - CJ_WEBSITE_ID=your_id'); 
    console.log('   - CJ_COMPANY_ID=your_company');
    console.log('');
    
    console.log('3. 🌐 Test scraping functions in browser console:');
    console.log('   Open any game page and check browser console for:');
    console.log('   ');
    console.log('   📦 MOCK MODE (if CJ_FORCE_REAL_DATA=false):');
    console.log('   - 🧪 Mock Gamivo price: USD 16.99 para "Game Name"');
    console.log('   - 🧪 Mock Fanatical price: USD 19.99 para "Game Name"');
    console.log('   ');
    console.log('   🔥 REAL MODE (if CJ_FORCE_REAL_DATA=true):');
    console.log('   - 🔥 CJ API: FORCED REAL DATA MODE');
    console.log('   - 🔥 REAL DATA MODE: Attempting real CJ API call');
    console.log('   - 💰 CJ/Gamivo: Preço encontrado USD X.XX');
    console.log('   - 💰 CJ/Fanatical: Preço encontrado USD X.XX');
    console.log('');
    
    console.log('4. 🔍 Check for error messages:');
    console.log('   - ❌ CJ API não está configurado');
    console.log('   - ❌ Nenhum produto Gamivo encontrado');
    console.log('   - ❌ CJ Gamivo Price Error');
    console.log('');
    
    console.log('5. 🎯 Expected behavior in price widget:');
    console.log('   - Should see Steam + Kinguin + Gamivo + Fanatical');
    console.log('   - Gamivo should have indigo color scheme');
    console.log('   - Fanatical should have red color scheme');
    console.log('');
    
    console.log('6. 📊 Debug steps:');
    console.log('   a) Open game page');
    console.log('   b) Open browser console (F12)');
    console.log('   c) Click "Force Update" button in price widget');
    console.log('   d) Watch console for CJ API messages');
    console.log('   e) Check if stores appear after update');
    console.log('');
    
    console.log('🚨 COMMON ISSUES:');
    console.log('   - Only Steam+Kinguin = CJ API not running or misconfigured');
    console.log('   - Mock data always = CJ_FORCE_REAL_DATA not set to true');
    console.log('   - Empty results = API credentials invalid or network error');
    console.log('   - Errors = Check browser console for specific error messages');
    console.log('');
    
    console.log('✅ QUICK FIX:');
    console.log('   1. Add to .env.local: CJ_FORCE_REAL_DATA=true');
    console.log('   2. Add your real CJ API credentials');
    console.log('   3. Restart: npm run dev');
    console.log('   4. Test on any game page');
    console.log('');
    
    console.log('🔧 If still not working, check:');
    console.log('   - Network connectivity to CJ API');
    console.log('   - API credentials validity');
    console.log('   - Affiliate program approval status');
    console.log('   - Browser console for detailed error messages');
}

simulateCJTest(); 